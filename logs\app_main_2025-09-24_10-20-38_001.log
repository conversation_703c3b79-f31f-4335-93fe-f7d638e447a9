========================================
AIStudio Real-time Log: main
Started: 2025-09-24T15:20:38.724Z
File: app_main_2025-09-24_10-20-38_001.log
========================================

[2025-09-24T15:20:38.976Z] [INFO] AIStudio application started successfully
[2025-09-24T15:20:38.976Z] [INFO] [main] AIStudio application started successfully
[2025-09-24T15:20:39.024Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-24T15:20:40.006Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-24T15:21:13.027Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-24_10-21-13_001.log
[2025-09-24T15:21:13.027Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-24T15:21:15.372Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-09-24T15:21:15.373Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-09-24T15:21:16.383Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-09-24T15:24:33.995Z] [INFO] [IPC Handler] Sending status: hunyuan_preprocessing - 1% - Preparing Hunyuan3D-2.1 pipeline…
[2025-09-24T15:24:39.639Z] [INFO] [IPC Handler] Sending status: hunyuan_generation - 5% - Starting generation…
