========================================
AIStudio Real-time Log: main
Started: 2025-09-24T15:39:14.266Z
File: app_main_2025-09-24_10-39-14_001.log
========================================

[2025-09-24T15:39:14.541Z] [INFO] AIStudio application started successfully
[2025-09-24T15:39:14.541Z] [INFO] [main] AIStudio application started successfully
[2025-09-24T15:39:14.593Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-24T15:39:15.607Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-24T15:39:48.631Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-24_10-39-48_001.log
[2025-09-24T15:39:48.631Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-24T15:39:51.060Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-09-24T15:39:51.061Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-09-24T15:39:52.072Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-09-24T15:53:37.835Z] [INFO] [IPC Handler] Sending status: hunyuan_preprocessing - 1% - Preparing Hunyuan3D-2.1 pipeline…
[2025-09-24T15:53:43.699Z] [INFO] [IPC Handler] Sending status: hunyuan_generation - 5% - Starting generation…
[2025-09-24T16:05:45.877Z] [INFO] [IPC Handler] Sending status: hunyuan_export - 100% - Export complete.
[2025-09-24T16:05:45.877Z] [INFO] [IPC Handler] Sending status: hunyuan_complete - 100% - Done.
